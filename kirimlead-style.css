/* ===== KIRIMLEAD EXCITING DESIGN SYSTEM ===== */
/* Bold, modern, and visually striking design */

/* CSS Variables - Enhanced Design Tokens */
:root {
  /* Primary Colors - More Vibrant */
  --primary-50: #ecfdf5;
  --primary-100: #d1fae5;
  --primary-200: #a7f3d0;
  --primary-300: #6ee7b7;
  --primary-400: #34d399;
  --primary-500: #10b981;
  --primary-600: #059669;
  --primary-700: #047857;
  --primary-800: #065f46;
  --primary-900: #064e3b;

  /* Accent Colors - Electric Blue */
  --accent-50: #eff6ff;
  --accent-100: #dbeafe;
  --accent-200: #bfdbfe;
  --accent-300: #93c5fd;
  --accent-400: #60a5fa;
  --accent-500: #3b82f6;
  --accent-600: #2563eb;
  --accent-700: #1d4ed8;
  --accent-800: #1e40af;
  --accent-900: #1e3a8a;

  /* Secondary Colors - Vibrant Purple */
  --secondary-50: #faf5ff;
  --secondary-100: #f3e8ff;
  --secondary-200: #e9d5ff;
  --secondary-300: #d8b4fe;
  --secondary-400: #c084fc;
  --secondary-500: #a855f7;
  --secondary-600: #9333ea;
  --secondary-700: #7c3aed;
  --secondary-800: #6b21a8;
  --secondary-900: #581c87;

  /* Tertiary Colors - Warm Orange */
  --tertiary-50: #fff7ed;
  --tertiary-100: #ffedd5;
  --tertiary-200: #fed7aa;
  --tertiary-300: #fdba74;
  --tertiary-400: #fb923c;
  --tertiary-500: #f97316;
  --tertiary-600: #ea580c;
  --tertiary-700: #c2410c;
  --tertiary-800: #9a3412;
  --tertiary-900: #7c2d12;

  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Semantic Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: var(--accent-500);

  /* Exciting Gradients */
  --gradient-primary: linear-gradient(
    135deg,
    var(--primary-500) 0%,
    var(--primary-600) 50%,
    var(--accent-500) 100%
  );
  --gradient-secondary: linear-gradient(
    135deg,
    var(--secondary-500) 0%,
    var(--secondary-600) 50%,
    var(--tertiary-500) 100%
  );
  --gradient-accent: linear-gradient(
    135deg,
    var(--accent-400) 0%,
    var(--accent-600) 50%,
    var(--secondary-500) 100%
  );
  --gradient-warm: linear-gradient(
    135deg,
    var(--tertiary-400) 0%,
    var(--tertiary-600) 50%,
    var(--primary-500) 100%
  );
  --gradient-hero: linear-gradient(
    135deg,
    var(--primary-50) 0%,
    var(--accent-50) 30%,
    var(--secondary-50) 60%,
    var(--tertiary-50) 100%
  );
  --gradient-card: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.7) 100%
  );
  --gradient-glass: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );

  /* Animated Gradients */
  --gradient-animated: linear-gradient(
    -45deg,
    var(--primary-400),
    var(--accent-400),
    var(--secondary-400),
    var(--tertiary-400)
  );

  /* Shadows - More Dramatic */
  --shadow-glow: 0 0 20px rgba(16, 185, 129, 0.3);
  --shadow-glow-accent: 0 0 20px rgba(59, 130, 246, 0.3);
  --shadow-glow-secondary: 0 0 20px rgba(168, 85, 247, 0.3);
  --shadow-dramatic: 0 20px 40px rgba(0, 0, 0, 0.1),
    0 10px 20px rgba(0, 0, 0, 0.05);
  --shadow-float: 0 10px 30px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.05);

  /* Glass Effects */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-backdrop: blur(10px);

  /* Typography */
  --font-family-sans: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, sans-serif;
  --font-family-mono: "JetBrains Mono", "Fira Code", monospace;

  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;

  /* Font Weights */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-32: 8rem;

  /* Border Radius */
  --radius-sm: 0.125rem;
  --radius: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-base: 200ms ease;
  --transition-slow: 300ms ease;
  --transition-slower: 500ms ease;

  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Dark mode variables */
:root[data-theme="dark"] {
  --bg-primary: var(--gray-900);
  --bg-secondary: var(--gray-800);
  --bg-tertiary: var(--gray-700);
  --text-primary: var(--gray-100);
  --text-secondary: var(--gray-300);
  --text-tertiary: var(--gray-400);
  --border-color: var(--gray-700);
}

/* Light mode variables (default) */
:root {
  --bg-primary: #ffffff;
  --bg-secondary: var(--gray-50);
  --bg-tertiary: var(--gray-100);
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-700);
  --text-tertiary: var(--gray-500);
  --border-color: var(--gray-200);
}

/* ===== RESET & BASE STYLES ===== */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family-sans);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--transition-base),
    color var(--transition-base);
}

/* ===== TYPOGRAPHY ===== */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  color: var(--text-primary);
}

h1 {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
}

h2 {
  font-size: var(--text-3xl);
}

h3 {
  font-size: var(--text-2xl);
}

h4 {
  font-size: var(--text-xl);
}

h5 {
  font-size: var(--text-lg);
}

h6 {
  font-size: var(--text-base);
}

p {
  margin: 0;
  line-height: var(--leading-relaxed);
  color: var(--text-secondary);
}

a {
  color: var(--accent-500);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--accent-600);
}

/* ===== LAYOUT COMPONENTS ===== */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.container-sm {
  max-width: 640px;
}

.container-md {
  max-width: 768px;
}

.container-lg {
  max-width: 1024px;
}

.container-xl {
  max-width: 1280px;
}

/* ===== HEADER & NAVIGATION ===== */
.header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.95);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) 0;
}

.logo {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--primary-500);
  text-decoration: none;
}

.nav {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.nav-links {
  display: flex;
  align-items: center;
  gap: var(--space-6);
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-link {
  color: var(--text-secondary);
  font-weight: var(--font-medium);
  transition: color var(--transition-fast);
  position: relative;
}

.nav-link:hover {
  color: var(--primary-500);
}

.nav-link.active {
  color: var(--primary-500);
}

.nav-link.active::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary-500);
  border-radius: var(--radius-full);
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  line-height: 1;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
}

.btn:focus {
  outline: 2px solid var(--accent-500);
  outline-offset: 2px;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-glow);
}

.btn-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  background: var(--gradient-primary);
  color: white;
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--shadow-glow), var(--shadow-dramatic);
}

.btn-secondary {
  background: var(--gradient-accent);
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-glow-accent);
}

.btn-secondary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.5s;
}

.btn-secondary:hover::before {
  left: 100%;
}

.btn-secondary:hover {
  background: var(--gradient-accent);
  color: white;
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--shadow-glow-accent), var(--shadow-dramatic);
}

.btn-outline {
  background: transparent;
  border-color: var(--border-color);
  color: var(--text-primary);
}

.btn-outline:hover {
  background: var(--bg-secondary);
  border-color: var(--primary-500);
  color: var(--primary-500);
}

.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
}

.btn-ghost:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-xs);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-base);
}

/* ===== CARDS - GLASSMORPHISM STYLE ===== */
.card {
  background: var(--gradient-card);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-float);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity var(--transition-base);
}

.card:hover::before {
  opacity: 1;
}

.card:hover {
  box-shadow: var(--shadow-dramatic);
  transform: translateY(-8px) scale(1.02);
  background: rgba(255, 255, 255, 0.95);
  border-color: var(--primary-200);
}

.card-header {
  margin-bottom: var(--space-4);
}

.card-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-2);
}

.card-description {
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.card-content {
  margin-bottom: var(--space-4);
}

.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-4);
}

/* ===== SECTIONS ===== */
.section {
  padding: var(--space-20) 0;
}

.section-sm {
  padding: var(--space-12) 0;
}

.section-lg {
  padding: var(--space-32) 0;
}

.section-hero {
  padding: var(--space-24) 0;
  background: linear-gradient(
    135deg,
    var(--primary-50) 0%,
    var(--accent-50) 100%
  );
}

.section-alt {
  background: var(--bg-secondary);
}

/* ===== HERO SECTION - EXCITING VERSION ===== */
.hero {
  text-align: center;
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: var(--font-extrabold);
  line-height: var(--leading-tight);
  margin-bottom: var(--space-6);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  animation: titleGlow 3s ease-in-out infinite alternate;
}

.hero-title::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: blur(20px);
  opacity: 0.3;
  z-index: -1;
  animation: titleGlow 3s ease-in-out infinite alternate;
}

.hero-subtitle {
  font-size: var(--text-xl);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-8);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  font-weight: var(--font-medium);
}

.hero-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-4);
  flex-wrap: wrap;
  margin-bottom: var(--space-12);
}

/* Hero Background Effects */
.section-hero {
  position: relative;
  overflow: hidden;
  background: var(--gradient-hero);
  padding: var(--space-32) 0;
}

.section-hero::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: var(--gradient-animated);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  opacity: 0.1;
  z-index: 1;
}

.section-hero::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 80%,
      rgba(16, 185, 129, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(59, 130, 246, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(168, 85, 247, 0.1) 0%,
      transparent 50%
    );
  z-index: 1;
}

/* ===== GRID LAYOUTS ===== */
.grid {
  display: grid;
  gap: var(--space-6);
}

.grid-cols-1 {
  grid-template-columns: repeat(1, 1fr);
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

/* ===== FEATURES GRID ===== */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-8);
  margin-top: var(--space-12);
}

.feature-card {
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  background: var(--gradient-card);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity var(--transition-base);
  z-index: -1;
}

.feature-card:hover::before {
  opacity: 0.05;
}

.feature-card:hover {
  transform: translateY(-8px) scale(1.03);
  box-shadow: var(--shadow-dramatic);
  border-color: var(--primary-300);
  background: rgba(255, 255, 255, 0.95);
}

.feature-icon {
  width: 64px;
  height: 64px;
  background: var(--gradient-primary);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-2xl);
  margin-bottom: var(--space-6);
  color: white;
  box-shadow: var(--shadow-glow);
  position: relative;
  overflow: hidden;
}

.feature-icon::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: var(--gradient-animated);
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
  opacity: 0.3;
}

.feature-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-2);
  color: var(--text-primary);
}

.feature-description {
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

/* ===== PRICING CARDS ===== */
.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-12);
}

.pricing-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  text-align: center;
  position: relative;
  transition: all var(--transition-base);
}

.pricing-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.pricing-card.featured {
  border-color: var(--primary-500);
  border-width: 2px;
  transform: scale(1.05);
}

.pricing-card.featured::before {
  content: "Populer";
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--primary-500);
  color: white;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.pricing-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-4);
}

.pricing-price {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--primary-500);
  margin-bottom: var(--space-2);
}

.pricing-period {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  margin-bottom: var(--space-6);
}

.pricing-features {
  list-style: none;
  padding: 0;
  margin: 0 0 var(--space-8) 0;
}

.pricing-features li {
  padding: var(--space-2) 0;
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.pricing-features li::before {
  content: "✓";
  color: var(--success);
  font-weight: var(--font-bold);
  margin-right: var(--space-2);
}

/* ===== TESTIMONIALS ===== */
.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-12);
}

.testimonial-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  transition: all var(--transition-base);
}

.testimonial-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.testimonial-content {
  font-style: italic;
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-4);
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.testimonial-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background: var(--primary-100);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-semibold);
  color: var(--primary-600);
}

.testimonial-info h4 {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-1);
}

.testimonial-info p {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

/* ===== FOOTER ===== */
.footer {
  background: var(--gray-900);
  color: var(--gray-300);
  padding: var(--space-16) 0 var(--space-8) 0;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-8);
  margin-bottom: var(--space-8);
}

.footer-section h4 {
  color: white;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-4);
}

.footer-section p {
  color: var(--gray-400);
  line-height: var(--leading-relaxed);
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: var(--space-2);
}

.footer-links a {
  color: var(--gray-400);
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: white;
}

.footer-bottom {
  border-top: 1px solid var(--gray-700);
  padding-top: var(--space-8);
  text-align: center;
  color: var(--gray-500);
  font-size: var(--text-sm);
}

/* ===== UTILITY CLASSES ===== */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: var(--primary-500);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-muted {
  color: var(--text-tertiary);
}

.bg-primary {
  background-color: var(--primary-500);
}

.bg-secondary {
  background-color: var(--bg-secondary);
}

.bg-white {
  background-color: white;
}

.rounded {
  border-radius: var(--radius);
}

.rounded-lg {
  border-radius: var(--radius-lg);
}

.rounded-xl {
  border-radius: var(--radius-xl);
}

.shadow {
  box-shadow: var(--shadow);
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

.mb-4 {
  margin-bottom: var(--space-4);
}

.mb-6 {
  margin-bottom: var(--space-6);
}

.mb-8 {
  margin-bottom: var(--space-8);
}

.mt-4 {
  margin-top: var(--space-4);
}

.mt-6 {
  margin-top: var(--space-6);
}

.mt-8 {
  margin-top: var(--space-8);
}

.p-4 {
  padding: var(--space-4);
}

.p-6 {
  padding: var(--space-6);
}

.p-8 {
  padding: var(--space-8);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .hero-title {
    font-size: var(--text-3xl);
  }

  .hero-subtitle {
    font-size: var(--text-lg);
  }

  .hero-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .nav-links {
    display: none;
  }

  .header-content {
    padding: var(--space-3) 0;
  }

  .section {
    padding: var(--space-12) 0;
  }

  .section-hero {
    padding: var(--space-16) 0;
  }

  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }

  .pricing-card.featured {
    transform: none;
  }

  .container {
    padding: 0 var(--space-3);
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: var(--text-2xl);
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .pricing-grid {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: var(--gray-900);
    --bg-secondary: var(--gray-800);
    --bg-tertiary: var(--gray-700);
    --text-primary: var(--gray-100);
    --text-secondary: var(--gray-300);
    --text-tertiary: var(--gray-400);
    --border-color: var(--gray-700);
  }

  .header {
    background: rgba(17, 24, 39, 0.95);
  }
}

/* ===== EXCITING ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes titleGlow {
  0% {
    filter: brightness(1) saturate(1);
  }
  100% {
    filter: brightness(1.2) saturate(1.3);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0);
  }
  40%,
  43% {
    transform: translate3d(0, -15px, 0);
  }
  70% {
    transform: translate3d(0, -7px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-2px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(2px);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-fade-in {
  animation: fadeIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-bounce {
  animation: bounce 2s infinite;
}

.animate-gradient {
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
}

/* ===== BACK TO TOP BUTTON ===== */
.back-to-top {
  position: fixed;
  bottom: var(--space-8);
  right: var(--space-8);
  width: 48px;
  height: 48px;
  background: var(--primary-500);
  color: white;
  border: none;
  border-radius: var(--radius-full);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-base);
  z-index: var(--z-fixed);
}

.back-to-top:hover {
  background: var(--primary-600);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}
