
/* Smooth Scroll */
html {
  scroll-behavior: smooth;
}

/* Button Global Style */
button, .btn {
  cursor: pointer;
  transition: all 0.3s ease;
}

/* Button Hover Effect */
button:hover, .btn:hover {
  background-color: #3A8C6E; /* kontras dari #2D6A4F tapi masih flat */
  transform: scale(1.05);
}

/* Flat Anchor Hover */
a:hover {
  color: #118AB2;
}

/* Title hover (misal judul harga, kursus) */
h2:hover, h3:hover {
  color: #2D6A4F;
  transition: color 0.2s ease-in-out;
}

/* Back to Top Button (if used) */
#backToTopBtn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 50%;
  background: #2D6A4F;
  color: white;
  cursor: pointer;
  font-size: 1.25rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  transition: all 0.3s ease;
}

#backToTopBtn:hover {
  background-color: #3A8C6E;
  transform: scale(1.2);
}
