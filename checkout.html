
<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>KirimLead</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap">
  <style>
    :root {
      --primary-color: #2D6A4F;
      --secondary-color: #F4A261;
      --accent-color: #118AB2;
      --bg-color: #ffffff;
      --text-color: #1E1E1E;
    }
    body.dark {
      --bg-color: #121212;
      --text-color: #E0E0E0;
    }
    body {
      margin: 0;
      font-family: 'Inter', sans-serif;
      background-color: var(--bg-color);
      color: var(--text-color);
      transition: background-color 0.3s, color 0.3s;
    }
    header, footer {
      padding: 1rem 2rem;
      background-color: var(--primary-color);
      color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    nav a {
      color: #fff;
      margin: 0 1rem;
      text-decoration: none;
      font-weight: 600;
    }
    .container {
      padding: 2rem;
      max-width: 1200px;
      margin: auto;
    }
    .dark-toggle {
      background: none;
      border: 2px solid #fff;
      color: #fff;
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      cursor: pointer;
    }
    .cookie-banner {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: var(--secondary-color);
      color: #000;
      display: none;
      justify-content: space-between;
      padding: 1rem 2rem;
      font-size: 0.9rem;
      align-items: center;
      z-index: 999;
    }
    .cookie-banner button {
      background-color: #fff;
      border: none;
      padding: 0.5rem 1rem;
      cursor: pointer;
      font-weight: bold;
    }
  </style>
  <link rel="stylesheet" href="kirimlead-style.css">
</head>
<body>
  <header>
    <div><strong>KirimLead</strong></div>
    <nav>
      <a href="index.html">Home</a>
      <a href="harga.html">Harga</a>
      <a href="akademi.html">Kursus</a>
      <a href="kontak.html">Kontak</a>
    </nav>
    <button class="dark-toggle" onclick="toggleDarkMode()">🌙 / ☀️</button>
  </header>

  <div class="container">
    <!-- Konten halaman di sini -->
    
    <section style="padding:3rem 1rem;max-width:800px;margin:auto;">
      <h1 style="text-align:center;font-size:2rem;margin-bottom:2rem;">Checkout Pembayaran</h1>
      <div style="background:#FAFAFA;padding:2rem;border-radius:12px;">
        <h3>Pesanan Kamu</h3>
        <label for="paket">Pilih Paket:</label>
        <select id="paket" name="paket" onchange="updateHarga()" style="width:100%;padding:0.75rem;border-radius:6px;border:1px solid #ccc;margin-bottom:1rem;">
          <option value="gratis">Gratis Trial - Rp 0</option>
          <option value="gold">Gold - Rp 57.000/bln</option>
          <option value="platinum">Platinum - Rp 97.000/bln</option>
        </select>
        <p><strong>Harga:</strong> <span id="harga">Rp 0</span></p>

        <hr style="margin:1.5rem 0;">
        <form action="#" method="POST" style="display:flex;flex-direction:column;gap:1rem;">
          <label for="name">Nama Lengkap</label>
          <input type="text" id="name" name="name" required style="padding:0.75rem;border-radius:6px;border:1px solid #ccc;">

          <label for="email">Email Aktif</label>
          <input type="email" id="email" name="email" required style="padding:0.75rem;border-radius:6px;border:1px solid #ccc;">

          <label for="wa">No WhatsApp</label>
          <input type="text" id="wa" name="wa" required style="padding:0.75rem;border-radius:6px;border:1px solid #ccc;">

          <label for="domain">Beli Domain?</label>
          <select id="domain" name="domain" style="padding:0.75rem;border-radius:6px;border:1px solid #ccc;">
            <option value="subdomain">Pakai subdomain gratis: xycd.kirimlead.net</option>
            <option value="customdomain">Tambah domain .com (+Rp 150.000)</option>
          </select>

          <label for="payment">Metode Pembayaran</label>
          <select id="payment" name="payment" required style="padding:0.75rem;border-radius:6px;border:1px solid #ccc;">
            <option value="">-- Pilih --</option>
            <option value="qris">QRIS</option>
            <option value="bca_va">Virtual Account BCA</option>
            <option value="bni_va">Virtual Account BNI</option>
            <option value="mandiri_va">Virtual Account Mandiri</option>
            <option value="manual_transfer">Transfer Manual</option>
          </select>

          <label style="display:flex;align-items:center;gap:0.5rem;font-size:0.9rem;">
            <input type="checkbox" required> Saya menyetujui <a href="#" style="color:#118AB2;text-decoration:underline;">Syarat & Ketentuan</a>
          </label>

          <button type="submit" style="margin-top:1rem;padding:0.75rem;background-color:#2D6A4F;color:#fff;border:none;border-radius:6px;font-weight:bold;">Bayar Sekarang</button>
        </form>
      </div>

      <div style="margin-top:2rem;text-align:center;font-size:0.9rem;color:#555;">
        <p>🔒 Data kamu aman bersama kami. Pembayaran diproses melalui sistem BuyTrack KirimLead.</p>
        <p>Jika mengalami kendala, hubungi CS via <a href="https://wa.me/*************" style="color:#118AB2;">WhatsApp di sini</a></p>
      </div>
    </section>

    <script>
      function updateHarga() {
        const paket = document.getElementById('paket').value;
        const hargaElem = document.getElementById('harga');
        if (paket === 'gratis') hargaElem.innerText = 'Rp 0';
        else if (paket === 'gold') hargaElem.innerText = 'Rp 57.000';
        else if (paket === 'platinum') hargaElem.innerText = 'Rp 97.000';
      }
    </script>
    
  </div>

  <footer>
    <div>© 2025 KirimLead</div>
    <div><a href="tos.html" style="color:#fff;text-decoration:underline;">Kebijakan Privasi</a></div>
  </footer>

  <div class="cookie-banner" id="cookieBanner">
    <span>Kami menggunakan cookie untuk pengalaman terbaik. <a href="tos.html">Pelajari lebih lanjut</a></span>
    <button onclick="acceptCookies()">Setuju</button>
  </div>

  <script>
    function toggleDarkMode() {
      document.body.classList.toggle('dark');
      localStorage.setItem('theme', document.body.classList.contains('dark') ? 'dark' : 'light');
    }
    function acceptCookies() {
      document.getElementById("cookieBanner").style.display = "none";
      localStorage.setItem("cookieAccepted", "yes");
    }
    if (!localStorage.getItem("cookieAccepted")) {
      document.getElementById("cookieBanner").style.display = "flex";
    }
    if (localStorage.getItem("theme") === "dark") {
      document.body.classList.add("dark");
    }
  </script>
</body>
</html>
