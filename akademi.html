<!DOCTYPE html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Akademi - KirimLead Learning Center</title>
    <meta
      name="description"
      content="Belajar digital marketing, copywriting, dan landing page optimization di KirimLead Akademi. Kursus online terlengkap untuk meningkatkan skill bisnis online Anda."
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
    />
    <link rel="stylesheet" href="kirimlead-style.css" />
  </head>
  <body>
    <!-- Header -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <a href="index.html" class="logo">KirimLead</a>

          <nav class="nav">
            <ul class="nav-links">
              <li><a href="index.html" class="nav-link">Home</a></li>
              <li><a href="harga.html" class="nav-link">Harga</a></li>
              <li><a href="akademi.html" class="nav-link active">Kursus</a></li>
              <li><a href="kontak.html" class="nav-link">Kontak</a></li>
            </ul>

            <div
              style="display: flex; align-items: center; gap: var(--space-4)"
            >
              <a href="login.html" class="btn btn-ghost">Masuk</a>
              <a href="harga.html" class="btn btn-primary">Coba Gratis</a>
            </div>
          </nav>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="section-hero">
      <div class="container">
        <div class="hero animate-fade-in-up">
          <h1 class="hero-title">Belajar Jualan Online Tanpa Bingung Sendiri</h1>
          <p class="hero-subtitle">
            KirimLead Akademi hadir untuk membantu Anda menguasai digital marketing, copywriting, landing page optimization, dan automasi AI.
            Semua materi praktis dan langsung bisa diterapkan untuk bisnis Anda.
          </p>

          <!-- Stats -->
          <div style="display: flex; justify-content: center; gap: var(--space-8); margin-top: var(--space-12); flex-wrap: wrap;">
            <div class="text-center">
              <div style="font-size: var(--text-2xl); font-weight: var(--font-bold); color: var(--primary-500);">50+</div>
              <div style="font-size: var(--text-sm); color: var(--text-secondary);">Kursus Tersedia</div>
            </div>
            <div class="text-center">
              <div style="font-size: var(--text-2xl); font-weight: var(--font-bold); color: var(--primary-500);">5000+</div>
              <div style="font-size: var(--text-sm); color: var(--text-secondary);">Student Aktif</div>
            </div>
            <div class="text-center">
              <div style="font-size: var(--text-2xl); font-weight: var(--font-bold); color: var(--primary-500);">4.8/5</div>
              <div style="font-size: var(--text-sm); color: var(--text-secondary);">Rating Rata-rata</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Courses Section -->
    <section class="section section-alt">
      <div class="container">
        <div class="text-center mb-8">
          <h2>Daftar Kelas Populer</h2>
          <p class="hero-subtitle" style="margin-top: var(--space-4);">
            Pilih kursus yang sesuai dengan kebutuhan bisnis Anda. Semua materi dibuat praktis dan mudah dipahami.
          </p>
        </div>
        <div class="grid grid-auto-fit" style="gap: var(--space-6);">
          <!-- Course Card 1 -->
          <div class="card" style="position: relative; overflow: hidden;">
            <div style="position: absolute; top: var(--space-3); left: var(--space-3); background: var(--error); color: white; padding: var(--space-1) var(--space-3); border-radius: var(--radius-full); font-size: var(--text-xs); font-weight: var(--font-semibold); z-index: 10;">
              Best Seller
            </div>
            <img
              src="https://filepaz.b-cdn.net/kirimlead/images/academy/image-academy-acad-5362oyu080325022939-8639G68080325022939.webp"
              alt="SellSpell"
              style="width: 100%; height: 200px; object-fit: cover; border-radius: var(--radius-lg) var(--radius-lg) 0 0;"
            />
            <div class="card-content">
              <h3 class="card-title">Workshop SellSpell</h3>
              <div style="display: flex; align-items: center; gap: var(--space-2); margin-bottom: var(--space-3);">
                <span style="color: var(--text-tertiary); text-decoration: line-through; font-size: var(--text-sm);">Rp 399.000</span>
                <span style="color: var(--primary-500); font-weight: var(--font-bold); font-size: var(--text-lg);">Rp 199.000</span>
              </div>
              <p class="card-description">Tulis kata-kata pemikat dengan teknik NLP yang konversinya tinggi. Pelajari copywriting yang mengkonversi.</p>
            </div>
            <div class="card-footer">
              <a href="#" class="btn btn-outline" style="width: 100%;">Lihat Detail</a>
            </div>
          </div>

          <!-- Course Card 2 -->
          <div class="card" style="position: relative; overflow: hidden;">
            <div style="position: absolute; top: var(--space-3); left: var(--space-3); background: var(--accent-500); color: white; padding: var(--space-1) var(--space-3); border-radius: var(--radius-full); font-size: var(--text-xs); font-weight: var(--font-semibold); z-index: 10;">
              New Arrival
            </div>
            <img
              src="https://filepaz.b-cdn.net/kirimlead/images/academy/image-academy-acad-18666h9281024032200-5245ELW281024032200.webp"
              alt="Meta Ads"
              style="width: 100%; height: 200px; object-fit: cover; border-radius: var(--radius-lg) var(--radius-lg) 0 0;"
            />
            <div class="card-content">
              <h3 class="card-title">Optimasi Meta Ads</h3>
              <div style="display: flex; align-items: center; gap: var(--space-2); margin-bottom: var(--space-3);">
                <span style="color: var(--text-tertiary); text-decoration: line-through; font-size: var(--text-sm);">Rp 499.000</span>
                <span style="color: var(--primary-500); font-weight: var(--font-bold); font-size: var(--text-lg);">Rp 249.000</span>
              </div>
              <p class="card-description">Strategi iklan Meta dari riset, testing sampai scaling tanpa boncos. Tingkatkan ROI iklan Anda.</p>
            </div>
            <div class="card-footer">
              <a href="#" class="btn btn-outline" style="width: 100%;">Lihat Detail</a>
            </div>
          </div>

          <!-- Course Card 3 -->
          <div class="card" style="overflow: hidden;">
            <img
              src="https://kirimlead.com/drive/images/academy/ACAD-7975R9M300124090706/image-academy-acad-7975r9m300124090706-6737045241124050951.webp"
              alt="SmartPage Builder"
              style="width: 100%; height: 200px; object-fit: cover; border-radius: var(--radius-lg) var(--radius-lg) 0 0;"
            />
            <div class="card-content">
              <h3 class="card-title">Kursus SmartPage Builder</h3>
              <div style="display: flex; align-items: center; gap: var(--space-2); margin-bottom: var(--space-3);">
                <span style="color: var(--text-tertiary); text-decoration: line-through; font-size: var(--text-sm);">Rp 299.000</span>
                <span style="color: var(--primary-500); font-weight: var(--font-bold); font-size: var(--text-lg);">Rp 149.000</span>
              </div>
              <p class="card-description">Langsung praktik bikin landing page ClickCanvas tanpa coding. Dari pemula hingga mahir.</p>
            </div>
            <div class="card-footer">
              <a href="#" class="btn btn-outline" style="width: 100%;">Lihat Detail</a>
            </div>
          </div>

          <!-- Course Card 4 -->
          <div class="card" style="overflow: hidden;">
            <img
              src="https://filepaz.b-cdn.net/kirimlead/images/academy/image-academy-acad-1125sk7261023071207-4796YLL241124052948.webp"
              alt="WhatsApp Automation"
              style="width: 100%; height: 200px; object-fit: cover; border-radius: var(--radius-lg) var(--radius-lg) 0 0;"
            />
            <div class="card-content">
              <h3 class="card-title">Panduan WhatsApp Automation</h3>
              <div style="display: flex; align-items: center; gap: var(--space-2); margin-bottom: var(--space-3);">
                <span style="color: var(--text-tertiary); text-decoration: line-through; font-size: var(--text-sm);">Rp 399.000</span>
                <span style="color: var(--primary-500); font-weight: var(--font-bold); font-size: var(--text-lg);">Rp 199.000</span>
              </div>
              <p class="card-description">Pelajari integrasi StarSender dan auto-followup pakai AI ChatGPT untuk automasi bisnis.</p>
            </div>
            <div class="card-footer">
              <a href="#" class="btn btn-outline" style="width: 100%;">Lihat Detail</a>
            </div>
          </div>

          <!-- Course Card 5 -->
          <div class="card" style="overflow: hidden;">
            <img
              src="https://kirimlead.com/drive/images/academy/ACAD-7975R9M300124090706/image-academy-acad-7975r9m300124090706-6737045241124050951.webp"
              alt="Advanced Landing Page"
              style="width: 100%; height: 200px; object-fit: cover; border-radius: var(--radius-lg) var(--radius-lg) 0 0;"
            />
            <div class="card-content">
              <h3 class="card-title">Advanced Landing Page</h3>
              <div style="display: flex; align-items: center; gap: var(--space-2); margin-bottom: var(--space-3);">
                <span style="color: var(--text-tertiary); text-decoration: line-through; font-size: var(--text-sm);">Rp 399.000</span>
                <span style="color: var(--primary-500); font-weight: var(--font-bold); font-size: var(--text-lg);">Rp 199.000</span>
              </div>
              <p class="card-description">Teknik advanced untuk optimasi conversion rate dan A/B testing landing page yang efektif.</p>
            </div>
            <div class="card-footer">
              <a href="#" class="btn btn-outline" style="width: 100%;">Lihat Detail</a>
            </div>
          </div>

          <!-- Course Card 6 -->
          <div class="card" style="position: relative; overflow: hidden;">
            <div style="position: absolute; top: var(--space-3); left: var(--space-3); background: var(--success); color: white; padding: var(--space-1) var(--space-3); border-radius: var(--radius-full); font-size: var(--text-xs); font-weight: var(--font-semibold); z-index: 10;">
              Popular
            </div>
            <img
              src="https://filepaz.b-cdn.net/kirimlead/images/academy/image-academy-acad-18666h9281024032200-5245ELW281024032200.webp"
              alt="Digital Marketing Mastery"
              style="width: 100%; height: 200px; object-fit: cover; border-radius: var(--radius-lg) var(--radius-lg) 0 0;"
            />
            <div class="card-content">
              <h3 class="card-title">Digital Marketing Mastery</h3>
              <div style="display: flex; align-items: center; gap: var(--space-2); margin-bottom: var(--space-3);">
                <span style="color: var(--text-tertiary); text-decoration: line-through; font-size: var(--text-sm);">Rp 599.000</span>
                <span style="color: var(--primary-500); font-weight: var(--font-bold); font-size: var(--text-lg);">Rp 299.000</span>
              </div>
              <p class="card-description">Kursus lengkap digital marketing dari SEO, SEM, Social Media hingga Email Marketing.</p>
            </div>
            <div class="card-footer">
              <a href="#" class="btn btn-outline" style="width: 100%;">Lihat Detail</a>
            </div>
          </div>
        </div>
      </section>

    <!-- Testimonials Section -->
    <section class="section">
      <div class="container">
        <div class="text-center mb-8">
          <h2>Kata Mereka Setelah Belajar di KirimLead Akademi</h2>
          <p class="hero-subtitle" style="margin-top: var(--space-4);">
            Lebih dari 5000+ student telah merasakan peningkatan skill dan income setelah belajar di KirimLead Akademi
          </p>
        </div>
        <div class="testimonials-grid">
          <div class="testimonial-card">
            <div class="testimonial-content">
              "Saya baru ngerti cara nulis iklan setelah ikut kelas SellSpell. Nggak pakai gaya lebay tapi closingnya nambah 150%! Materinya praktis dan langsung bisa diterapin."
            </div>
            <div class="testimonial-author">
              <div class="testimonial-avatar">R</div>
              <div class="testimonial-info">
                <h4>Rina</h4>
                <p>Penjual Skincare</p>
              </div>
            </div>
          </div>

          <div class="testimonial-card">
            <div class="testimonial-content">
              "Kelas Meta Ads-nya rapi banget. Saya jadi ngerti kenapa iklan saya kemarin boncos, dan sekarang udah mulai ROI. Dalam 2 bulan ROI naik 300%!"
            </div>
            <div class="testimonial-author">
              <div class="testimonial-avatar">D</div>
              <div class="testimonial-info">
                <h4>Deni</h4>
                <p>Dropshipper</p>
              </div>
            </div>
          </div>

          <div class="testimonial-card">
            <div class="testimonial-content">
              "Saya bikin landing page sendiri pakai ClickCanvas setelah ikut kelas. Gampang dan langsung dapat leads. Conversion rate naik dari 2% jadi 8%!"
            </div>
            <div class="testimonial-author">
              <div class="testimonial-avatar">Y</div>
              <div class="testimonial-info">
                <h4>Yusuf</h4>
                <p>Coach Online</p>
              </div>
            </div>
          </div>

          <div class="testimonial-card">
            <div class="testimonial-content">
              "WhatsApp automation course-nya game changer! Sekarang follow-up customer otomatis, closing rate naik 200%. Tim saya jadi lebih efisien."
            </div>
            <div class="testimonial-author">
              <div class="testimonial-avatar">S</div>
              <div class="testimonial-info">
                <h4>Sari</h4>
                <p>Digital Agency Owner</p>
              </div>
            </div>
          </div>

          <div class="testimonial-card">
            <div class="testimonial-content">
              "Materi copywriting SellSpell benar-benar mengubah cara saya menulis. Sales letter saya sekarang lebih persuasif dan conversion meningkat drastis."
            </div>
            <div class="testimonial-author">
              <div class="testimonial-avatar">A</div>
              <div class="testimonial-info">
                <h4>Ahmad</h4>
                <p>Content Creator</p>
              </div>
            </div>
          </div>

          <div class="testimonial-card">
            <div class="testimonial-content">
              "Kursus SmartPage Builder sangat membantu. Sekarang saya bisa bikin landing page sendiri tanpa developer. Hemat biaya dan lebih cepat!"
            </div>
            <div class="testimonial-author">
              <div class="testimonial-avatar">L</div>
              <div class="testimonial-info">
                <h4>Lisa</h4>
                <p>Online Business Owner</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Final CTA Section -->
    <section class="section section-alt">
      <div class="container">
        <div class="container-md text-center">
          <h2 class="mb-6">Bingung Mulai dari Mana?</h2>
          <p style="font-size: var(--text-lg); color: var(--text-secondary); margin-bottom: var(--space-6);">
            Coba satu kelas dulu yang paling sesuai kebutuhan Anda. Semua materi bisa diakses seumur hidup dan belajar sesuai waktu Anda sendiri.
          </p>
          <div style="background: var(--primary-50); padding: var(--space-6); border-radius: var(--radius-xl); margin-bottom: var(--space-8);">
            <p style="font-weight: var(--font-semibold); color: var(--primary-600); margin-bottom: var(--space-2);">
              🎓 5000+ student aktif dengan rating rata-rata 4.8/5
            </p>
            <p style="font-size: var(--text-sm); color: var(--text-secondary);">
              Akses seumur hidup • Sertifikat completion • Community support
            </p>
          </div>
          <div class="hero-actions">
            <a href="login.html" class="btn btn-primary btn-lg">Gabung Kelas Sekarang</a>
            <a href="kontak.html" class="btn btn-outline btn-lg">Konsultasi Gratis</a>
          </div>
        </div>
      </div>
    </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <h4>KirimLead Akademi</h4>
          <p>Platform pembelajaran online terlengkap untuk digital marketing, copywriting, dan landing page optimization. Tingkatkan skill bisnis online Anda bersama kami.</p>
        </div>

        <div class="footer-section">
          <h4>Kursus Populer</h4>
          <ul class="footer-links">
            <li><a href="#sellspell">Workshop SellSpell</a></li>
            <li><a href="#meta-ads">Optimasi Meta Ads</a></li>
            <li><a href="#smartpage">SmartPage Builder</a></li>
            <li><a href="#whatsapp">WhatsApp Automation</a></li>
          </ul>
        </div>

        <div class="footer-section">
          <h4>Support</h4>
          <ul class="footer-links">
            <li><a href="kontak.html">Kontak</a></li>
            <li><a href="#help">Pusat Bantuan</a></li>
            <li><a href="#community">Community</a></li>
            <li><a href="#faq">FAQ</a></li>
          </ul>
        </div>

        <div class="footer-section">
          <h4>KirimLead</h4>
          <ul class="footer-links">
            <li><a href="index.html">Homepage</a></li>
            <li><a href="harga.html">Harga</a></li>
            <li><a href="login.html">Login</a></li>
            <li><a href="#about">Tentang Kami</a></li>
          </ul>
        </div>
      </div>

      <div class="footer-bottom">
        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: var(--space-4);">
          <p>&copy; 2025 KirimLead Akademi. All rights reserved.</p>
          <div style="display: flex; gap: var(--space-6);">
            <a href="#privacy" style="color: var(--gray-500); font-size: var(--text-sm);">Kebijakan Privasi</a>
            <a href="#terms" style="color: var(--gray-500); font-size: var(--text-sm);">Syarat & Ketentuan</a>
          </div>
        </div>
      </div>
    </div>
  </footer>

  <!-- Back to Top Button -->
  <button class="back-to-top" onclick="window.scrollTo({top: 0, behavior: 'smooth'})" title="Kembali ke atas">
    ↑
  </button>

  <!-- Scripts -->
  <script>
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    // Show/hide back to top button
    window.addEventListener('scroll', function() {
      const backToTop = document.querySelector('.back-to-top');
      if (window.pageYOffset > 300) {
        backToTop.style.opacity = '1';
        backToTop.style.visibility = 'visible';
      } else {
        backToTop.style.opacity = '0';
        backToTop.style.visibility = 'hidden';
      }
    });

    // Initialize back to top button
    document.addEventListener('DOMContentLoaded', function() {
      const backToTop = document.querySelector('.back-to-top');
      backToTop.style.opacity = '0';
      backToTop.style.visibility = 'hidden';
      backToTop.style.transition = 'opacity 0.3s, visibility 0.3s';
    });

    // Add animation on scroll
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-fade-in-up');
        }
      });
    }, observerOptions);

    // Observe elements for animation
    document.addEventListener('DOMContentLoaded', function() {
      const animateElements = document.querySelectorAll('.card, .testimonial-card');
      animateElements.forEach(el => observer.observe(el));
    });
  </script>
  </body>
</html>
