<!DOCTYPE html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Harga - KirimLead Landing Page Builder</title>
    <meta
      name="description"
      content="<PERSON>lih paket KirimLead yang sesuai kebutuhan bisnis <PERSON>. <PERSON><PERSON> gratis, upgrade kapan saja. Harga transparan tanpa biaya tersembunyi."
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
    />
    <link rel="stylesheet" href="kirimlead-style.css" />
  </head>
  <body>
    <!-- Header -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <a href="index.html" class="logo">KirimLead</a>

          <nav class="nav">
            <ul class="nav-links">
              <li><a href="index.html" class="nav-link">Home</a></li>
              <li><a href="harga.html" class="nav-link active">Harga</a></li>
              <li><a href="akademi.html" class="nav-link">Kursus</a></li>
              <li><a href="kontak.html" class="nav-link">Kontak</a></li>
            </ul>

            <div
              style="display: flex; align-items: center; gap: var(--space-4)"
            >
              <a href="login.html" class="btn btn-ghost">Masuk</a>
              <a href="#pricing" class="btn btn-primary">Coba Gratis</a>
            </div>
          </nav>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="section-hero">
      <div class="container">
        <div class="hero animate-fade-in-up">
          <h1 class="hero-title">Harga yang Masuk Akal, Fitur yang Maksimal</h1>
          <p class="hero-subtitle">
            Kami percaya jualan online tidak harus mahal. Mulai dari gratis,
            Anda sudah bisa membangun halaman penjualan yang siap mengkonversi.
            Tanpa biaya tersembunyi, tanpa kontrak jangka panjang.
          </p>

          <!-- Pricing Toggle -->
          <div
            style="
              display: flex;
              justify-content: center;
              margin-top: var(--space-8);
            "
          >
            <div
              style="
                background: var(--bg-secondary);
                padding: var(--space-1);
                border-radius: var(--radius-lg);
                display: flex;
              "
            >
              <button
                class="btn btn-sm"
                id="monthlyBtn"
                style="background: var(--primary-500); color: white"
              >
                Bulanan
              </button>
              <button class="btn btn-sm btn-ghost" id="yearlyBtn">
                Tahunan
                <span
                  style="
                    background: var(--success);
                    color: white;
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-size: 10px;
                    margin-left: 4px;
                  "
                  >-20%</span
                >
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="section section-alt">
      <div class="container">
        <div class="pricing-grid">
          <div class="pricing-card">
            <div class="pricing-title">Gratis Trial</div>
            <div class="pricing-price">Rp 0</div>
            <div class="pricing-period">Selamanya</div>
            <ul class="pricing-features">
              <li>1 Domain custom</li>
              <li>5 Halaman landing page</li>
              <li>Basic templates</li>
              <li>Form builder</li>
              <li>SSL certificate</li>
              <li>Email support</li>
              <li>KirimLead branding</li>
            </ul>
            <a
              href="checkout.html"
              class="btn btn-secondary"
              style="width: 100%"
              >Coba Sekarang</a
            >
          </div>

          <div class="pricing-card featured">
            <div class="pricing-title">Gold</div>
            <div class="pricing-price" id="goldPrice">Rp 57.000</div>
            <div class="pricing-period" id="goldPeriod">per bulan</div>
            <ul class="pricing-features">
              <li>5 Domain custom</li>
              <li>3000 Halaman landing page</li>
              <li>Premium templates</li>
              <li>WhatsApp integration</li>
              <li>Payment gateway (Tripay)</li>
              <li>Analytics dashboard</li>
              <li>Google Tag Manager</li>
              <li>Priority support</li>
              <li>Remove KirimLead branding</li>
            </ul>
            <a href="checkout.html" class="btn btn-primary" style="width: 100%"
              >Pilih Paket Ini</a
            >
          </div>

          <div class="pricing-card">
            <div class="pricing-title">Platinum</div>
            <div class="pricing-price" id="platinumPrice">Rp 97.000</div>
            <div class="pricing-period" id="platinumPeriod">per bulan</div>
            <ul class="pricing-features">
              <li>10 Domain custom</li>
              <li>5000 Halaman landing page</li>
              <li>All premium templates</li>
              <li>Advanced automation</li>
              <li>LMS features</li>
              <li>Affiliate system</li>
              <li>Custom branding</li>
              <li>API access</li>
              <li>White-label solution</li>
              <li>24/7 priority support</li>
            </ul>
            <a href="checkout.html" class="btn btn-primary" style="width: 100%"
              >Langganan Sekarang</a
            >
          </div>
        </div>

        <div class="text-center mt-8">
          <p
            style="
              color: var(--text-secondary);
              font-size: var(--text-sm);
              margin-bottom: var(--space-4);
            "
          >
            Semua paket termasuk hosting, SSL, dan update gratis. Tidak ada
            biaya setup atau biaya tersembunyi.
          </p>
          <div
            style="
              background: var(--primary-50);
              padding: var(--space-4);
              border-radius: var(--radius-lg);
              display: inline-block;
            "
          >
            <p
              style="
                font-weight: var(--font-semibold);
                color: var(--primary-600);
                margin: 0;
              "
            >
              💰 Garansi uang kembali 30 hari untuk semua paket berbayar
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="section">
      <div class="container">
        <div class="container-md">
          <div class="text-center mb-8">
            <h2>Pertanyaan yang Sering Diajukan</h2>
            <p class="hero-subtitle" style="margin-top: var(--space-4)">
              Masih ada pertanyaan? Tim support kami siap membantu Anda 24/7
            </p>
          </div>

          <div style="display: grid; gap: var(--space-4)">
            <div class="card">
              <h4 style="margin-bottom: var(--space-2)">
                Apakah ada biaya tersembunyi?
              </h4>
              <p style="color: var(--text-secondary)">
                Tidak ada biaya tersembunyi sama sekali. Harga yang tertera
                sudah termasuk hosting, SSL, bandwidth, dan semua fitur yang
                disebutkan.
              </p>
            </div>

            <div class="card">
              <h4 style="margin-bottom: var(--space-2)">
                Bisakah saya upgrade atau downgrade kapan saja?
              </h4>
              <p style="color: var(--text-secondary)">
                Ya, Anda bisa upgrade atau downgrade paket kapan saja. Perubahan
                akan berlaku pada periode billing berikutnya.
              </p>
            </div>

            <div class="card">
              <h4 style="margin-bottom: var(--space-2)">
                Bagaimana dengan garansi uang kembali?
              </h4>
              <p style="color: var(--text-secondary)">
                Kami memberikan garansi uang kembali 30 hari untuk semua paket
                berbayar. Jika tidak puas, uang akan dikembalikan 100%.
              </p>
            </div>

            <div class="card">
              <h4 style="margin-bottom: var(--space-2)">
                Apakah domain sudah termasuk?
              </h4>
              <p style="color: var(--text-secondary)">
                Paket sudah termasuk subdomain gratis (namaanda.kirimlead.com).
                Untuk domain custom, Anda bisa beli melalui dashboard kami atau
                connect domain yang sudah ada.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Final CTA Section -->
    <section class="section section-alt">
      <div class="container">
        <div class="container-md text-center">
          <h2 class="mb-6">Siap Memulai Bisnis Online yang Menguntungkan?</h2>
          <p
            style="
              font-size: var(--text-lg);
              color: var(--text-secondary);
              margin-bottom: var(--space-6);
            "
          >
            Bergabung dengan 3000+ bisnis yang sudah merasakan peningkatan
            penjualan dengan KirimLead. Mulai gratis hari ini, tidak perlu kartu
            kredit.
          </p>
          <div
            style="
              background: var(--primary-50);
              padding: var(--space-6);
              border-radius: var(--radius-xl);
              margin-bottom: var(--space-8);
            "
          >
            <p
              style="
                font-weight: var(--font-semibold);
                color: var(--primary-600);
                margin-bottom: var(--space-2);
              "
            >
              🚀 400+ bisnis baru bergabung dalam 30 hari terakhir
            </p>
            <p style="font-size: var(--text-sm); color: var(--text-secondary)">
              Rata-rata peningkatan conversion rate 30% dalam 3 bulan pertama
            </p>
          </div>
          <div class="hero-actions">
            <a href="#pricing" class="btn btn-primary btn-lg"
              >Mulai Trial Gratis</a
            >
            <a href="kontak.html" class="btn btn-outline btn-lg"
              >Konsultasi Gratis</a
            >
          </div>
        </div>
      </div>
    </section>
    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h4>KirimLead</h4>
            <p>
              Platform landing page builder all-in-one yang membantu bisnis
              Indonesia meningkatkan penjualan online dengan fitur lengkap dan
              mudah digunakan.
            </p>
          </div>

          <div class="footer-section">
            <h4>Produk</h4>
            <ul class="footer-links">
              <li><a href="index.html#features">Fitur</a></li>
              <li><a href="harga.html">Harga</a></li>
              <li><a href="template-global.html">Template</a></li>
              <li><a href="akademi.html">Akademi</a></li>
            </ul>
          </div>

          <div class="footer-section">
            <h4>Support</h4>
            <ul class="footer-links">
              <li><a href="kontak.html">Kontak</a></li>
              <li><a href="#help">Pusat Bantuan</a></li>
              <li><a href="#docs">Dokumentasi</a></li>
              <li><a href="#api">API</a></li>
            </ul>
          </div>

          <div class="footer-section">
            <h4>Perusahaan</h4>
            <ul class="footer-links">
              <li><a href="login.html">Login</a></li>
              <li><a href="#about">Tentang Kami</a></li>
              <li><a href="#careers">Karir</a></li>
              <li><a href="#blog">Blog</a></li>
            </ul>
          </div>
        </div>

        <div class="footer-bottom">
          <div
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
              flex-wrap: wrap;
              gap: var(--space-4);
            "
          >
            <p>&copy; 2025 KirimLead. All rights reserved.</p>
            <div style="display: flex; gap: var(--space-6)">
              <a
                href="#privacy"
                style="color: var(--gray-500); font-size: var(--text-sm)"
                >Kebijakan Privasi</a
              >
              <a
                href="#terms"
                style="color: var(--gray-500); font-size: var(--text-sm)"
                >Syarat & Ketentuan</a
              >
            </div>
          </div>
        </div>
      </div>
    </footer>

    <!-- Back to Top Button -->
    <button
      class="back-to-top"
      onclick="window.scrollTo({top: 0, behavior: 'smooth'})"
      title="Kembali ke atas"
    >
      ↑
    </button>

    <!-- Scripts -->
    <script>
      // Pricing toggle functionality
      const monthlyBtn = document.getElementById("monthlyBtn");
      const yearlyBtn = document.getElementById("yearlyBtn");
      const goldPrice = document.getElementById("goldPrice");
      const platinumPrice = document.getElementById("platinumPrice");
      const goldPeriod = document.getElementById("goldPeriod");
      const platinumPeriod = document.getElementById("platinumPeriod");

      const monthlyPrices = {
        gold: "Rp 57.000",
        platinum: "Rp 97.000",
      };

      const yearlyPrices = {
        gold: "Rp 456.000",
        platinum: "Rp 776.000",
      };

      monthlyBtn.addEventListener("click", function () {
        monthlyBtn.style.background = "var(--primary-500)";
        monthlyBtn.style.color = "white";
        yearlyBtn.style.background = "transparent";
        yearlyBtn.style.color = "var(--text-secondary)";

        goldPrice.textContent = monthlyPrices.gold;
        platinumPrice.textContent = monthlyPrices.platinum;
        goldPeriod.textContent = "per bulan";
        platinumPeriod.textContent = "per bulan";
      });

      yearlyBtn.addEventListener("click", function () {
        yearlyBtn.style.background = "var(--primary-500)";
        yearlyBtn.style.color = "white";
        monthlyBtn.style.background = "transparent";
        monthlyBtn.style.color = "var(--text-secondary)";

        goldPrice.textContent = yearlyPrices.gold;
        platinumPrice.textContent = yearlyPrices.platinum;
        goldPeriod.textContent = "per tahun";
        platinumPeriod.textContent = "per tahun";
      });

      // Smooth scrolling for anchor links
      document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute("href"));
          if (target) {
            target.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          }
        });
      });

      // Show/hide back to top button
      window.addEventListener("scroll", function () {
        const backToTop = document.querySelector(".back-to-top");
        if (window.pageYOffset > 300) {
          backToTop.style.opacity = "1";
          backToTop.style.visibility = "visible";
        } else {
          backToTop.style.opacity = "0";
          backToTop.style.visibility = "hidden";
        }
      });

      // Initialize back to top button
      document.addEventListener("DOMContentLoaded", function () {
        const backToTop = document.querySelector(".back-to-top");
        backToTop.style.opacity = "0";
        backToTop.style.visibility = "hidden";
        backToTop.style.transition = "opacity 0.3s, visibility 0.3s";
      });

      // Add animation on scroll
      const observerOptions = {
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px",
      };

      const observer = new IntersectionObserver(function (entries) {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up");
          }
        });
      }, observerOptions);

      // Observe elements for animation
      document.addEventListener("DOMContentLoaded", function () {
        const animateElements = document.querySelectorAll(
          ".pricing-card, .card"
        );
        animateElements.forEach((el) => observer.observe(el));
      });
    </script>
  </body>
</html>
