
/* Smooth Scroll */
html {
  scroll-behavior: smooth;
}

/* Button Global Style */
button, .btn {
  cursor: pointer;
  transition: all 0.3s ease;
}

/* Button Hover Effect */
button:hover, .btn:hover {
  background-color: #3A8C6E; /* kontras dari #2D6A4F tapi masih flat */
  transform: scale(1.05);
}

/* Flat Anchor Hover */
a:hover {
  color: #118AB2;
}

/* Title hover (misal judul harga, kursus) */
h2:hover, h3:hover {
  color: #2D6A4F;
  transition: color 0.2s ease-in-out;
}

/* Back to Top Button (if used) */
#backToTopBtn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 50%;
  background: #2D6A4F;
  color: white;
  cursor: pointer;
  font-size: 1.25rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  transition: all 0.3s ease;
}

#backToTopBtn:hover {
  background-color: #3A8C6E;
  transform: scale(1.2);
}


/* Theme Color Variables */
:root {
  --bg-light: #ffffff;
  --bg-dark: #1a1a1a;
  --text-light: #000000;
  --text-dark: #f0f0f0;
  --primary: #2D6A4F;
  --primary-hover: #3A8C6E;
  --accent: #118AB2;
}

/* Default light mode */
body {
  background-color: var(--bg-light);
  color: var(--text-light);
}

/* Dark mode via class toggle */
body.dark {
  background-color: var(--bg-dark);
  color: var(--text-dark);
}

body.dark a {
  color: var(--accent);
}

body.dark h1, 
body.dark h2, 
body.dark h3, 
body.dark h4, 
body.dark h5, 
body.dark h6 {
  color: var(--text-dark);
}

body.dark .btn,
body.dark button,
body.dark input,
body.dark textarea,
body.dark select {
  background-color: #333;
  color: #f0f0f0;
  border-color: #555;
}

body.dark button:hover {
  background-color: var(--primary-hover);
}

/* Footer adjustments for dark */
body.dark footer {
  background-color: #111;
  color: var(--text-dark);
}

/* Back to top in dark mode */
body.dark #backToTopBtn {
  background: #555;
  color: white;
}

body.dark #backToTopBtn:hover {
  background-color: #777;
}
