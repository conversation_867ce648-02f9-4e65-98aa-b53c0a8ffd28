<!DOCTYPE html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Kontak - KirimLead Customer Support</title>
    <meta
      name="description"
      content="Hubungi tim support KirimLead untuk bantuan, pertanyaan, atau konsultasi gratis. Kami siap membantu Anda 24/7 melalui WhatsApp, email, atau form kontak."
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
    />
    <link rel="stylesheet" href="kirimlead-style.css" />
  </head>
  <body>
    <!-- Header -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <a href="index.html" class="logo">KirimLead</a>

          <nav class="nav">
            <ul class="nav-links">
              <li><a href="index.html" class="nav-link">Home</a></li>
              <li><a href="harga.html" class="nav-link">Harga</a></li>
              <li><a href="akademi.html" class="nav-link">Kursus</a></li>
              <li><a href="kontak.html" class="nav-link active">Kontak</a></li>
            </ul>

            <div
              style="display: flex; align-items: center; gap: var(--space-4)"
            >
              <a href="login.html" class="btn btn-ghost">Masuk</a>
              <a href="harga.html" class="btn btn-primary">Coba Gratis</a>
            </div>
          </nav>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="section-hero">
      <div class="container">
        <div class="hero animate-fade-in-up">
          <h1 class="hero-title">Ada Pertanyaan? Kami Siap Bantu</h1>
          <p class="hero-subtitle">
            Tim support KirimLead siap membantu Anda 24/7. Hubungi kami untuk bantuan teknis, konsultasi bisnis, atau pertanyaan seputar fitur platform.
          </p>

          <!-- Quick Contact Stats -->
          <div style="display: flex; justify-content: center; gap: var(--space-8); margin-top: var(--space-12); flex-wrap: wrap;">
            <div class="text-center">
              <div style="font-size: var(--text-2xl); font-weight: var(--font-bold); color: var(--primary-500);">< 5 min</div>
              <div style="font-size: var(--text-sm); color: var(--text-secondary);">Response Time</div>
            </div>
            <div class="text-center">
              <div style="font-size: var(--text-2xl); font-weight: var(--font-bold); color: var(--primary-500);">24/7</div>
              <div style="font-size: var(--text-sm); color: var(--text-secondary);">Support Available</div>
            </div>
            <div class="text-center">
              <div style="font-size: var(--text-2xl); font-weight: var(--font-bold); color: var(--primary-500);">99%</div>
              <div style="font-size: var(--text-sm); color: var(--text-secondary);">Customer Satisfaction</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Methods Section -->
    <section class="section section-alt">
      <div class="container">
        <div class="grid grid-auto-fit" style="gap: var(--space-8);">
          <!-- Contact Information Card -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">Hubungi Customer Service</h3>
              <p class="card-description">Tim support kami siap membantu Anda dengan respon cepat dan solusi terbaik</p>
            </div>

            <div class="card-content">
              <div style="display: flex; flex-direction: column; gap: var(--space-4);">
                <div style="display: flex; align-items: center; gap: var(--space-3);">
                  <div style="width: 40px; height: 40px; background: var(--primary-100); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center;">
                    👤
                  </div>
                  <div>
                    <div style="font-weight: var(--font-semibold);">Osman Abdullah</div>
                    <div style="font-size: var(--text-sm); color: var(--text-secondary);">Customer Success Manager</div>
                  </div>
                </div>

                <div style="display: flex; align-items: center; gap: var(--space-3);">
                  <div style="width: 40px; height: 40px; background: var(--success); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; color: white;">
                    📱
                  </div>
                  <div>
                    <a href="https://wa.me/6281260007189" class="btn btn-outline btn-sm">
                      WhatsApp: 0812-6000-7189
                    </a>
                  </div>
                </div>

                <div style="display: flex; align-items: center; gap: var(--space-3);">
                  <div style="width: 40px; height: 40px; background: var(--success); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; color: white;">
                    📱
                  </div>
                  <div>
                    <a href="https://wa.me/6281299957734" class="btn btn-outline btn-sm">
                      WhatsApp: 0812-9995-7734
                    </a>
                  </div>
                </div>

                <div style="display: flex; align-items: center; gap: var(--space-3);">
                  <div style="width: 40px; height: 40px; background: var(--accent-100); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center;">
                    📍
                  </div>
                  <div>
                    <div style="font-weight: var(--font-medium);">Klaten, Jawa Tengah</div>
                    <div style="font-size: var(--text-sm); color: var(--text-secondary);">Indonesia</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Contact Form Card -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">Kirim Pesan Langsung</h3>
              <p class="card-description">Isi form di bawah ini dan kami akan merespon dalam waktu kurang dari 5 menit</p>
            </div>

            <div class="card-content">
              <form action="#" method="POST" style="display: flex; flex-direction: column; gap: var(--space-4);">
                <div>
                  <label style="display: block; margin-bottom: var(--space-2); font-weight: var(--font-medium); color: var(--text-primary);">Nama Lengkap</label>
                  <input
                    type="text"
                    placeholder="Masukkan nama lengkap Anda"
                    required
                    style="width: 100%; padding: var(--space-3); border: 1px solid var(--border-color); border-radius: var(--radius-lg); font-size: var(--text-base); transition: border-color var(--transition-fast);"
                    onfocus="this.style.borderColor='var(--primary-500)'"
                    onblur="this.style.borderColor='var(--border-color)'"
                  />
                </div>

                <div>
                  <label style="display: block; margin-bottom: var(--space-2); font-weight: var(--font-medium); color: var(--text-primary);">Email Aktif</label>
                  <input
                    type="email"
                    placeholder="<EMAIL>"
                    required
                    style="width: 100%; padding: var(--space-3); border: 1px solid var(--border-color); border-radius: var(--radius-lg); font-size: var(--text-base); transition: border-color var(--transition-fast);"
                    onfocus="this.style.borderColor='var(--primary-500)'"
                    onblur="this.style.borderColor='var(--border-color)'"
                  />
                </div>

                <div>
                  <label style="display: block; margin-bottom: var(--space-2); font-weight: var(--font-medium); color: var(--text-primary);">Pesan</label>
                  <textarea
                    placeholder="Tulis pertanyaan atau pesan Anda di sini..."
                    required
                    rows="5"
                    style="width: 100%; padding: var(--space-3); border: 1px solid var(--border-color); border-radius: var(--radius-lg); font-size: var(--text-base); resize: vertical; transition: border-color var(--transition-fast);"
                    onfocus="this.style.borderColor='var(--primary-500)'"
                    onblur="this.style.borderColor='var(--border-color)'"
                  ></textarea>
                </div>

                <button type="submit" class="btn btn-primary" style="width: 100%;">
                  Kirim Pesan
                </button>
              </form>
            </div>
          </div>
      </div>
    </section>

    <!-- Office Location Section -->
    <section class="section">
      <div class="container">
        <div class="text-center mb-8">
          <h2>Atau Kunjungi Kantor Kami</h2>
          <p style="color: var(--text-secondary); margin-top: var(--space-4);">
            Kantor pusat KirimLead berlokasi di Klaten, Jawa Tengah. Kami menerima kunjungan dengan appointment terlebih dahulu.
          </p>
        </div>

        <div class="container-lg">
          <div class="card" style="overflow: hidden; padding: 0;">
            <iframe
              src="https://maps.google.com/maps?q=Jl.%20Kh.%20Ahmad%20Dahlan%20Gg.%20Haris%20Moedjahid%2C%20Sidorejo%2C%20Belang%20Wetan%2C%20Klaten&t=&z=15&ie=UTF8&iwloc=&output=embed"
              width="100%"
              height="400"
              frameborder="0"
              style="border: 0; border-radius: var(--radius-xl);"
              allowfullscreen
            ></iframe>
          </div>

          <div style="text-align: center; margin-top: var(--space-6);">
            <div style="background: var(--primary-50); padding: var(--space-4); border-radius: var(--radius-lg); display: inline-block;">
              <p style="font-weight: var(--font-semibold); color: var(--primary-600); margin: 0;">
                📍 Jl. Kh. Ahmad Dahlan Gg. Haris Moedjahid, Sidorejo, Belang Wetan, Klaten
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <h4>KirimLead Support</h4>
          <p>Tim customer success kami siap membantu Anda mencapai target bisnis dengan platform KirimLead. Hubungi kami kapan saja!</p>
          <div style="margin-top: var(--space-4);">
            <p style="font-size: var(--text-sm); color: var(--gray-400);">
              📧 <EMAIL><br>
              📱 +62 812-6000-7189<br>
              🏢 Klaten, Jawa Tengah
            </p>
          </div>
        </div>

        <div class="footer-section">
          <h4>Quick Links</h4>
          <ul class="footer-links">
            <li><a href="index.html">Homepage</a></li>
            <li><a href="harga.html">Harga</a></li>
            <li><a href="akademi.html">Akademi</a></li>
            <li><a href="login.html">Login</a></li>
          </ul>
        </div>

        <div class="footer-section">
          <h4>Support</h4>
          <ul class="footer-links">
            <li><a href="#help">Pusat Bantuan</a></li>
            <li><a href="#docs">Dokumentasi</a></li>
            <li><a href="#community">Community</a></li>
            <li><a href="#status">Status</a></li>
          </ul>
        </div>

        <div class="footer-section">
          <h4>Contact Hours</h4>
          <ul class="footer-links">
            <li>Senin - Jumat: 08:00 - 22:00</li>
            <li>Sabtu - Minggu: 09:00 - 18:00</li>
            <li>WhatsApp: 24/7</li>
            <li>Email: 24/7</li>
          </ul>
        </div>
      </div>

      <div class="footer-bottom">
        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: var(--space-4);">
          <p>&copy; 2025 KirimLead. All rights reserved.</p>
          <div style="display: flex; gap: var(--space-6);">
            <a href="#privacy" style="color: var(--gray-500); font-size: var(--text-sm);">Kebijakan Privasi</a>
            <a href="#terms" style="color: var(--gray-500); font-size: var(--text-sm);">Syarat & Ketentuan</a>
          </div>
        </div>
      </div>
    </div>
  </footer>

  <!-- Back to Top Button -->
  <button class="back-to-top" onclick="window.scrollTo({top: 0, behavior: 'smooth'})" title="Kembali ke atas">
    ↑
  </button>

  <!-- Scripts -->
  <script>
    // Form enhancement
    document.addEventListener('DOMContentLoaded', function() {
      const form = document.querySelector('form');
      if (form) {
        form.addEventListener('submit', function(e) {
          e.preventDefault();

          // Show success message
          const button = form.querySelector('button[type="submit"]');
          const originalText = button.textContent;
          button.textContent = 'Mengirim...';
          button.disabled = true;

          // Simulate form submission
          setTimeout(() => {
            button.textContent = 'Pesan Terkirim! ✓';
            button.style.background = 'var(--success)';

            setTimeout(() => {
              button.textContent = originalText;
              button.style.background = 'var(--primary-500)';
              button.disabled = false;
              form.reset();
            }, 2000);
          }, 1000);
        });
      }
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    // Show/hide back to top button
    window.addEventListener('scroll', function() {
      const backToTop = document.querySelector('.back-to-top');
      if (window.pageYOffset > 300) {
        backToTop.style.opacity = '1';
        backToTop.style.visibility = 'visible';
      } else {
        backToTop.style.opacity = '0';
        backToTop.style.visibility = 'hidden';
      }
    });

    // Initialize back to top button
    document.addEventListener('DOMContentLoaded', function() {
      const backToTop = document.querySelector('.back-to-top');
      backToTop.style.opacity = '0';
      backToTop.style.visibility = 'hidden';
      backToTop.style.transition = 'opacity 0.3s, visibility 0.3s';
    });

    // Add animation on scroll
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-fade-in-up');
        }
      });
    }, observerOptions);

    // Observe elements for animation
    document.addEventListener('DOMContentLoaded', function() {
      const animateElements = document.querySelectorAll('.card');
      animateElements.forEach(el => observer.observe(el));
    });
  </script>
  </body>
</html>
